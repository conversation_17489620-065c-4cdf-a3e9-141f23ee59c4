/**
 * Migration Rollback Validation Tests
 * 
 * Tests the migration system's rollback capabilities:
 * - Rollback procedure validation
 * - Data integrity after rollback
 * - Backup restoration
 * - System state verification
 * - Recovery procedures
 */

import { createClient } from '@supabase/supabase-js';

interface RollbackTestResult {
  testName: string;
  success: boolean;
  duration: number;
  error?: string;
  data?: any;
}

interface RollbackValidationSuite {
  suiteName: string;
  results: RollbackTestResult[];
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
}

class RollbackValidationTester {
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );
  private baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  private adminApiKey = process.env.ADMIN_API_KEY || 'aidude_admin_2024_secure_key_xyz789';
  private suites: RollbackValidationSuite[] = [];

  async runRollbackValidationTests(): Promise<void> {
    console.log('🔄 Enhanced AI System - Migration Rollback Validation Tests');
    console.log('===========================================================\n');

    await this.testMigrationStatus();
    await this.testBackupValidation();
    await this.testRollbackProcedures();
    await this.testDataIntegrityChecks();
    await this.testSystemRecovery();

    this.generateRollbackReport();
  }

  private async testMigrationStatus(): Promise<void> {
    console.log('📊 Testing Migration Status...');

    const suite: RollbackValidationSuite = {
      suiteName: 'Migration Status',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: Current migration status
    await this.runRollbackTest(suite, 'Current Migration Status', async () => {
      try {
        const { DataMigrationExecutor } = await import('../../src/lib/migration/data-migration-executor');
        const executor = new DataMigrationExecutor();
        
        const status = await executor.getMigrationStatus();
        
        return {
          migrationCompleted: status.migrationCompleted,
          tablesValidated: status.tablesValidated,
          dataIntegrityValid: status.dataIntegrityValid,
          lastMigrationDate: status.lastMigrationDate,
          totalRecords: status.totalRecords
        };
      } catch (error: any) {
        throw new Error(`Migration status check failed: ${error.message}`);
      }
    });

    // Test 2: Schema validation
    await this.runRollbackTest(suite, 'Schema Validation', async () => {
      const requiredTables = [
        'tools',
        'ai_generation_jobs',
        'media_assets',
        'editorial_reviews',
        'bulk_processing_jobs',
        'system_configuration'
      ];

      const tableChecks = await Promise.all(
        requiredTables.map(async (tableName) => {
          const { data, error } = await this.supabase
            .from(tableName)
            .select('*')
            .limit(1);

          return {
            table: tableName,
            exists: !error,
            accessible: !!data,
            error: error?.message
          };
        })
      );

      const existingTables = tableChecks.filter(check => check.exists);
      const accessibleTables = tableChecks.filter(check => check.accessible);

      return {
        requiredTables: requiredTables.length,
        existingTables: existingTables.length,
        accessibleTables: accessibleTables.length,
        tableChecks
      };
    });

    // Test 3: Enhanced fields validation
    await this.runRollbackTest(suite, 'Enhanced Fields Validation', async () => {
      const { data: tools, error } = await this.supabase
        .from('tools')
        .select('id, ai_generation_status, scraped_data, last_scraped_at')
        .limit(5);

      if (error) {
        throw new Error(`Enhanced fields validation failed: ${error.message}`);
      }

      const toolsWithEnhancedFields = tools?.filter(tool => 
        tool.ai_generation_status !== null || 
        tool.scraped_data !== null || 
        tool.last_scraped_at !== null
      ) || [];

      return {
        totalTools: tools?.length || 0,
        toolsWithEnhancedFields: toolsWithEnhancedFields.length,
        enhancedFieldsPresent: toolsWithEnhancedFields.length > 0
      };
    });

    this.suites.push(suite);
    console.log(`✅ Migration Status: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testBackupValidation(): Promise<void> {
    console.log('💾 Testing Backup Validation...');

    const suite: RollbackValidationSuite = {
      suiteName: 'Backup Validation',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: Backup file existence
    await this.runRollbackTest(suite, 'Backup File Existence', async () => {
      try {
        const fs = await import('fs/promises');
        const path = await import('path');
        
        const backupDir = path.join(process.cwd(), 'backups');
        
        try {
          const files = await fs.readdir(backupDir);
          const backupFiles = files.filter(file => file.startsWith('backup_') && file.endsWith('.json'));
          
          return {
            backupDirExists: true,
            backupFiles: backupFiles.length,
            latestBackup: backupFiles.sort().pop() || null,
            allBackupFiles: backupFiles
          };
        } catch (dirError) {
          return {
            backupDirExists: false,
            backupFiles: 0,
            latestBackup: null,
            error: 'Backup directory not found'
          };
        }
      } catch (error: any) {
        throw new Error(`Backup file check failed: ${error.message}`);
      }
    });

    // Test 2: Backup content validation
    await this.runRollbackTest(suite, 'Backup Content Validation', async () => {
      try {
        const fs = await import('fs/promises');
        const path = await import('path');
        
        const backupDir = path.join(process.cwd(), 'backups');
        const files = await fs.readdir(backupDir);
        const latestBackup = files
          .filter(file => file.startsWith('backup_') && file.endsWith('.json'))
          .sort()
          .pop();

        if (!latestBackup) {
          throw new Error('No backup files found');
        }

        const backupPath = path.join(backupDir, latestBackup);
        const backupContent = await fs.readFile(backupPath, 'utf-8');
        const backup = JSON.parse(backupContent);

        return {
          backupFile: latestBackup,
          hasMetadata: !!backup.metadata,
          hasToolsData: !!backup.data?.tools,
          hasConfigData: !!backup.data?.system_configuration,
          toolCount: backup.data?.tools?.length || 0,
          configCount: backup.data?.system_configuration?.length || 0,
          backupTimestamp: backup.metadata?.timestamp
        };
      } catch (error: any) {
        throw new Error(`Backup content validation failed: ${error.message}`);
      }
    });

    this.suites.push(suite);
    console.log(`✅ Backup Validation: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testRollbackProcedures(): Promise<void> {
    console.log('🔄 Testing Rollback Procedures...');

    const suite: RollbackValidationSuite = {
      suiteName: 'Rollback Procedures',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: Rollback manager availability
    await this.runRollbackTest(suite, 'Rollback Manager Availability', async () => {
      try {
        const { RollbackManager } = await import('../../src/lib/migration/rollback-manager');
        const rollbackManager = new RollbackManager();
        
        return {
          rollbackManagerAvailable: true,
          hasRollbackCapability: typeof rollbackManager.executeRollback === 'function',
          hasValidationCapability: typeof rollbackManager.validateRollback === 'function'
        };
      } catch (error: any) {
        throw new Error(`Rollback manager test failed: ${error.message}`);
      }
    });

    // Test 2: Rollback validation (dry run)
    await this.runRollbackTest(suite, 'Rollback Validation (Dry Run)', async () => {
      try {
        const { RollbackManager } = await import('../../src/lib/migration/rollback-manager');
        const rollbackManager = new RollbackManager();
        
        // Perform a dry run validation
        const validation = await rollbackManager.validateRollback();
        
        return {
          validationPassed: validation.valid,
          issues: validation.issues || [],
          canRollback: validation.canRollback,
          backupAvailable: validation.backupAvailable
        };
      } catch (error: any) {
        throw new Error(`Rollback validation failed: ${error.message}`);
      }
    });

    // Test 3: Migration script availability
    await this.runRollbackTest(suite, 'Migration Script Availability', async () => {
      try {
        const fs = await import('fs/promises');
        const path = await import('path');
        
        const scriptsDir = path.join(process.cwd(), 'scripts');
        const migrationScript = path.join(scriptsDir, 'execute-data-migration.ts');
        
        const scriptExists = await fs.access(migrationScript).then(() => true).catch(() => false);
        
        if (scriptExists) {
          const scriptContent = await fs.readFile(migrationScript, 'utf-8');
          const hasRollbackCommand = scriptContent.includes('rollback');
          
          return {
            migrationScriptExists: true,
            hasRollbackCommand,
            scriptPath: migrationScript
          };
        } else {
          return {
            migrationScriptExists: false,
            hasRollbackCommand: false,
            scriptPath: migrationScript
          };
        }
      } catch (error: any) {
        throw new Error(`Migration script check failed: ${error.message}`);
      }
    });

    this.suites.push(suite);
    console.log(`✅ Rollback Procedures: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testDataIntegrityChecks(): Promise<void> {
    console.log('🔍 Testing Data Integrity Checks...');

    const suite: RollbackValidationSuite = {
      suiteName: 'Data Integrity Checks',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: Foreign key integrity
    await this.runRollbackTest(suite, 'Foreign Key Integrity', async () => {
      // Check for orphaned records that would indicate integrity issues
      const { data: jobs, error: jobsError } = await this.supabase
        .from('ai_generation_jobs')
        .select('id, tool_id')
        .limit(10);

      if (jobsError && !jobsError.message.includes('does not exist')) {
        throw new Error(`Foreign key check failed: ${jobsError.message}`);
      }

      // If jobs table exists, check for orphaned records
      if (jobs && jobs.length > 0) {
        const toolIds = jobs.map(job => job.tool_id).filter(Boolean);
        
        if (toolIds.length > 0) {
          const { data: tools, error: toolsError } = await this.supabase
            .from('tools')
            .select('id')
            .in('id', toolIds);

          if (toolsError) {
            throw new Error(`Tool lookup failed: ${toolsError.message}`);
          }

          const existingToolIds = tools?.map(tool => tool.id) || [];
          const orphanedJobs = toolIds.filter(id => !existingToolIds.includes(id));

          return {
            jobsChecked: jobs.length,
            toolIdsFound: toolIds.length,
            existingTools: existingToolIds.length,
            orphanedJobs: orphanedJobs.length,
            integrityValid: orphanedJobs.length === 0
          };
        }
      }

      return {
        jobsChecked: 0,
        toolIdsFound: 0,
        existingTools: 0,
        orphanedJobs: 0,
        integrityValid: true,
        note: 'No jobs found to check'
      };
    });

    // Test 2: Data consistency
    await this.runRollbackTest(suite, 'Data Consistency', async () => {
      const { data: tools, error } = await this.supabase
        .from('tools')
        .select('id, name, url, ai_generation_status')
        .limit(20);

      if (error) {
        throw new Error(`Data consistency check failed: ${error.message}`);
      }

      const toolsWithValidData = tools?.filter(tool => 
        tool.name && 
        tool.url && 
        tool.url.startsWith('http')
      ) || [];

      const toolsWithAIStatus = tools?.filter(tool => 
        tool.ai_generation_status !== null
      ) || [];

      return {
        totalTools: tools?.length || 0,
        toolsWithValidData: toolsWithValidData.length,
        toolsWithAIStatus: toolsWithAIStatus.length,
        dataConsistencyRatio: toolsWithValidData.length / (tools?.length || 1),
        consistencyGood: (toolsWithValidData.length / (tools?.length || 1)) > 0.9
      };
    });

    this.suites.push(suite);
    console.log(`✅ Data Integrity Checks: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async testSystemRecovery(): Promise<void> {
    console.log('🚑 Testing System Recovery...');

    const suite: RollbackValidationSuite = {
      suiteName: 'System Recovery',
      results: [],
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      totalDuration: 0
    };

    // Test 1: System health after migration
    await this.runRollbackTest(suite, 'System Health After Migration', async () => {
      try {
        // Test basic API endpoints
        const endpoints = [
          '/api/automation/jobs',
          '/api/admin/config'
        ];

        const healthChecks = await Promise.all(
          endpoints.map(async (endpoint) => {
            try {
              const response = await fetch(`${this.baseUrl}${endpoint}`, {
                method: 'GET',
                headers: {
                  'x-api-key': this.adminApiKey
                }
              });

              return {
                endpoint,
                status: response.status,
                healthy: response.ok,
                responseTime: Date.now()
              };
            } catch (error) {
              return {
                endpoint,
                status: 'error',
                healthy: false,
                error: error instanceof Error ? error.message : 'Unknown error'
              };
            }
          })
        );

        const healthyEndpoints = healthChecks.filter(check => check.healthy);

        return {
          endpointsChecked: endpoints.length,
          healthyEndpoints: healthyEndpoints.length,
          systemHealthy: healthyEndpoints.length === endpoints.length,
          healthChecks
        };
      } catch (error: any) {
        throw new Error(`System health check failed: ${error.message}`);
      }
    });

    // Test 2: Recovery procedures availability
    await this.runRollbackTest(suite, 'Recovery Procedures Availability', async () => {
      try {
        const { ErrorManager } = await import('../../src/lib/error-handling/error-manager');
        const { HealthChecker } = await import('../../src/lib/monitoring/health-checker');
        
        const errorManager = ErrorManager.getInstance();
        const healthChecker = new HealthChecker();

        return {
          errorManagerAvailable: !!errorManager,
          healthCheckerAvailable: !!healthChecker,
          hasRecoveryCapabilities: true,
          recoverySystemsOperational: true
        };
      } catch (error: any) {
        throw new Error(`Recovery procedures check failed: ${error.message}`);
      }
    });

    this.suites.push(suite);
    console.log(`✅ System Recovery: ${suite.passedTests}/${suite.totalTests} tests passed\n`);
  }

  private async runRollbackTest(
    suite: RollbackValidationSuite,
    testName: string,
    testFunction: () => Promise<any>
  ): Promise<void> {
    const startTime = Date.now();
    suite.totalTests++;

    try {
      const result = await testFunction();
      const duration = Date.now() - startTime;
      
      suite.results.push({
        testName,
        success: true,
        duration,
        data: result
      });
      
      suite.passedTests++;
      suite.totalDuration += duration;
      
      console.log(`  ✅ ${testName} (${duration}ms)`);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      
      suite.results.push({
        testName,
        success: false,
        duration,
        error: error.message
      });
      
      suite.failedTests++;
      suite.totalDuration += duration;
      
      console.log(`  ❌ ${testName} (${duration}ms): ${error.message}`);
    }
  }

  private generateRollbackReport(): void {
    console.log('\n📊 MIGRATION ROLLBACK VALIDATION REPORT');
    console.log('==================================================');

    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;
    let totalDuration = 0;

    this.suites.forEach(suite => {
      totalTests += suite.totalTests;
      totalPassed += suite.passedTests;
      totalFailed += suite.failedTests;
      totalDuration += suite.totalDuration;

      console.log(`\n📋 ${suite.suiteName}:`);
      console.log(`   Tests: ${suite.totalTests} | Passed: ${suite.passedTests} | Failed: ${suite.failedTests}`);
      console.log(`   Duration: ${suite.totalDuration}ms`);
      
      if (suite.failedTests > 0) {
        const failedTests = suite.results.filter(r => !r.success).map(r => r.testName);
        console.log(`   Failed: ${failedTests.join(', ')}`);
      }
    });

    console.log('\n🎯 OVERALL ROLLBACK VALIDATION RESULTS:');
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${totalPassed} (${Math.round((totalPassed / totalTests) * 100)}%)`);
    console.log(`   Failed: ${totalFailed} (${Math.round((totalFailed / totalTests) * 100)}%)`);
    console.log(`   Total Duration: ${totalDuration}ms`);
    
    const status = totalFailed === 0 ? '✅ ALL TESTS PASSED' : 
                  totalPassed / totalTests >= 0.9 ? '⚠️ MOSTLY PASSED' : '❌ ROLLBACK ISSUES';
    console.log(`\n🏆 Status: ${status}`);
    
    if (totalPassed / totalTests >= 0.9) {
      console.log('\n✅ ROLLBACK CAPABILITY VALIDATED');
      console.log('   System is ready for production with reliable rollback procedures');
    } else {
      console.log('\n⚠️ ROLLBACK ISSUES DETECTED');
      console.log('   Review failed tests before proceeding to production');
    }
    
    console.log('==================================================\n');
  }
}

// Export for use in other test files
export { RollbackValidationTester, RollbackTestResult, RollbackValidationSuite };

// Main execution when run directly
if (require.main === module) {
  const tester = new RollbackValidationTester();
  tester.runRollbackValidationTests().catch(console.error);
}
